<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            style="?attr/textAppearanceHeadline5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Configuración del Sistema"
            android:textColor="?android:attr/textColorPrimary"
             />

        <!-- Configuración de Conexión -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="CONFIGURACIÓN DE CONEXIÓN"
                     />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Servidor:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigServidor"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Base de Datos:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigBaseDatos"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Punto de Venta:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigPuntoVenta"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Información del Punto de Venta -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="INFORMACIÓN DEL PUNTO DE VENTA"
                     />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Nombre Punto de Venta:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigNombrePuntoVenta"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Prefijo Punto de Venta:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigPrefijoPuntoVenta"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Información del Vendedor -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="INFORMACIÓN DEL VENDEDOR"
                     />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Usuario Vendedor:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigCodigoVendedor"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Cotización -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="COTIZACIÓN"
                     />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Valor Cotización:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigCotizacion"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Configuración de APIs -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="CONFIGURACIÓN DE APIS"
                     />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Código Empresa:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigCodEmpresa"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Código Sucursal:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigCodSucursal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="URL API:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigUrlApi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Usuario API:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigUsuarioApi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Clave API:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigClaveApi"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- API Secundaria -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="CONFIGURACIÓN API SECUNDARIA"
                     />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="URL API 2:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigUrlApi2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Usuario API 2:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigUsuarioApi2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Clave API 2:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigClaveApi2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Código Empresa 2:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigCodEmpresa2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Código Sucursal 2:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvConfigCodSucursal2"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                         />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</ScrollView>
