<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardElevation="8dp"
    android:layout_margin="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="24dp">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Escanea el QR para realizar el pago"
            android:textSize="18dp"
            android:textAppearance="?attr/textAppearanceHeadline6"
            android:layout_marginBottom="16dp"/>

        <ImageView
            android:id="@+id/qrImageView"
            android:layout_width="400dp"
            android:layout_height="400dp"
            android:layout_gravity="center"/>

        <TextView
            android:id="@+id/textHookAlias"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Hook Alias"
            android:textStyle="bold"
            android:layout_marginTop="16dp"
            android:textAlignment="center"
            android:textColor="?attr/colorOnSurface"
            android:textAppearance="?attr/textAppearanceBody1"
            android:visibility="gone"/>

        <ProgressBar
            android:id="@+id/progressBar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"/>

        <!-- Spinner y texto para procesamiento de pago -->
        <LinearLayout
            android:id="@+id/layoutProcesandoPago"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="16dp"
            android:visibility="gone">

            <ProgressBar
                android:id="@+id/progressBarProcesando"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"/>

            <TextView
                android:id="@+id/textProcesandoPago"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="Procesando Pago..."
                android:textAppearance="?attr/textAppearanceSubtitle1"
                android:textStyle="bold"
                android:textColor="#4CAF50"
                android:layout_marginTop="8dp"/>

        </LinearLayout>

        <com.google.android.material.button.MaterialButton
            android:id="@+id/btnClose"
            style="?attr/materialButtonOutlinedStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="Cancelar"
            android:layout_marginTop="24dp"/>

    </LinearLayout>
</androidx.cardview.widget.CardView>