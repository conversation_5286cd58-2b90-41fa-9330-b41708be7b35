<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/nav_header_height"
    android:background="@color/primary_blue_600"
    android:gravity="bottom"
    android:orientation="vertical"
    android:padding="16dp"
    android:theme="@style/ThemeOverlay.AppCompat.Dark">

    <ImageView
        android:id="@+id/imageView"
        android:layout_width="wrap_content"
        android:layout_height="100dp"
        android:contentDescription="@string/nav_header_desc"
        android:padding="5dp"
        app:srcCompat="@mipmap/logosalesmovile" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="@dimen/nav_header_vertical_spacing"
        android:text="@string/nav_header_title"
        android:textStyle="bold"
        android:textSize="16dp"
        android:textColor="@color/text_on_primary"
        android:textAppearance="@style/TextAppearance.AppCompat.Body1" />

</LinearLayout>

    <!-- android:background="@drawable/side_nav_bar" -->