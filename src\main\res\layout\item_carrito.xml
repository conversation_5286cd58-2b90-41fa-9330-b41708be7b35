<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="8dp"
    android:layout_marginVertical="4dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <ImageView
            android:id="@+id/productImage"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:contentDescription="Imagen del producto"
            android:src="@drawable/ic_product_placeholder"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <TextView
            android:id="@+id/productName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toStartOf="@+id/btnRemove"
            app:layout_constraintStart_toEndOf="@id/productImage"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Nombre del Producto" />

        <TextView
            android:id="@+id/productSize"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/black"
            android:textSize="12sp"
            app:layout_constraintStart_toStartOf="@+id/productName"
            app:layout_constraintTop_toBottomOf="@+id/productName"
            tools:text="Tamaño: Mediano" />

        <TextView
            android:id="@+id/productPrice"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/alt4_primary_navy_900"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@+id/productName"
            app:layout_constraintTop_toBottomOf="@+id/productSize"
            tools:text="$150.00" />

        <TextView
            android:id="@+id/productQuantity"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/grey"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="@+id/productName"
            app:layout_constraintTop_toBottomOf="@+id/productPrice"
            app:layout_constraintBottom_toBottomOf="parent"
            tools:text="Cantidad: 2"
            />

        <ImageButton
            android:id="@+id/btnRemove"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Eliminar producto"
            android:src="@drawable/ic_delete"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>
