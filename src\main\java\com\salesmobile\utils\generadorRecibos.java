package com.salesmobile.utils;

import android.content.Context;
import android.util.Base64;
import android.util.Log;

import com.android.volley.DefaultRetryPolicy;
import com.android.volley.RequestQueue;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.Volley;
import com.salesmobile.config.ParametrosConf;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class generadorRecibos {

    public interface ReciboCallback {
        void onSuccess(String pdfClientePath, String pdfComercioPath);
        void onError(String errorMessage);
    }

    public static void generarRecibo(Context context, JSONObject paymentData, String puestoventa, ReciboCallback callback) {
        try {
            if (puestoventa == null || puestoventa.isEmpty()) {
                Log.d("RECIBO_ERROR", "Punto de venta no configurado");
                if (callback != null) {
                    callback.onError("Punto de venta no configurado");
                }
                return;
            }

            JSONObject payload = new JSONObject();

            payload.put("commerce_name", paymentData.optString("commerce_name"));
            payload.put("branch_name", paymentData.optString("branch_name"));
            payload.put("puestoventa", puestoventa);
            payload.put("amount", paymentData.optString("amount"));
            payload.put("currency", paymentData.optString("currency"));
            payload.put("installment_number", paymentData.optString("installment_number"));

            String dateTime = paymentData.optString("date_time", "");
            if (dateTime.isEmpty()) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
                dateTime = sdf.format(new Date());
            }

            payload.put("date_time", dateTime);
            payload.put("ticket_number", paymentData.optString("ticket_number", ""));
            payload.put("authorization_code", paymentData.optString("authorization_code", ""));
            payload.put("merchant_code", paymentData.optString("merchant_code", ""));
            payload.put("card_last_numbers", paymentData.optString("card_last_numbers", ""));
            payload.put("account_type", paymentData.optString("account_type", "CREDITO"));

            Log.d("RECIBO_PAYLOAD", "Payload: " + payload);

            String baseUrl = ParametrosConf.BASE_URL;
            RequestQueue queue = Volley.newRequestQueue(context);
            JsonObjectRequest jsonRequest = new JsonObjectRequest(
                    com.android.volley.Request.Method.POST,
                    baseUrl + "generarReciboPago.php",
                    payload,
                    response -> {
                        try {
                            if (response.has("success") && response.getBoolean("success")) {
                                String pdfBase64Cliente = response.getString("pdfb64Cliente");
                                String pdfBase64Comercio = response.getString("pdfb64Comercio");

                                guardarRecibosPDF(context, pdfBase64Cliente, pdfBase64Comercio, callback);
                            } else {
                                Log.e("RECIBO_ERROR", "Error en la respuesta del servidor");
                                if (callback != null) {
                                    callback.onError("Error en la respuesta del servidor");
                                }
                            }
                        } catch (JSONException e) {
                            Log.e("RECIBO_ERROR", "Error procesando respuesta", e);
                            if (callback != null) {
                                callback.onError("Error procesando respuesta: " + e.getMessage());
                            }
                        }
                    },
                    error -> {
                        Log.e("RECIBO_ERROR", "Error en la llamada API", error);
                        if (callback != null) {
                            callback.onError("Error en la llamada API: " + error.getMessage());
                        }
                    }
            );

            jsonRequest.setRetryPolicy(new DefaultRetryPolicy(
                    15000,
                    1,
                    DefaultRetryPolicy.DEFAULT_BACKOFF_MULT
            ));

            queue.add(jsonRequest);

        } catch (JSONException e) {
            Log.e("RECIBO_API", "Error creating recibo request: " + e.getMessage());
            if (callback != null) {
                callback.onError("Error creating recibo request: " + e.getMessage());
            }
        }
    }

    private static void guardarRecibosPDF(Context context, String pdfBase64Cliente, String pdfBase64Comercio, ReciboCallback callback) {
        try {
            File tempDir = new File(context.getCacheDir(), "temp");
            if (!tempDir.exists()) {
                tempDir.mkdirs();
            }

            long timestamp = System.currentTimeMillis();

            byte[] pdfBytesCliente = Base64.decode(pdfBase64Cliente, Base64.DEFAULT);
            String fileNameCliente = "recibo_cliente_" + timestamp + ".pdf";
            File pdfFileCliente = new File(tempDir, fileNameCliente);
            FileOutputStream fosCliente = new FileOutputStream(pdfFileCliente);
            fosCliente.write(pdfBytesCliente);
            fosCliente.close();
            String reciboPdfClientePath = pdfFileCliente.getAbsolutePath();

            byte[] pdfBytesComercio = Base64.decode(pdfBase64Comercio, Base64.DEFAULT);
            String fileNameComercio = "recibo_comercio_" + timestamp + ".pdf";
            File pdfFileComercio = new File(tempDir, fileNameComercio);
            FileOutputStream fosComercio = new FileOutputStream(pdfFileComercio);
            fosComercio.write(pdfBytesComercio);
            fosComercio.close();
            String reciboPdfComercioPath = pdfFileComercio.getAbsolutePath();

            Log.d("RECIBO_PDF", "PDF Cliente guardado en: " + reciboPdfClientePath);
            Log.d("RECIBO_PDF", "PDF Comercio guardado en: " + reciboPdfComercioPath);

            if (callback != null) {
                callback.onSuccess(reciboPdfClientePath, reciboPdfComercioPath);
            }

        } catch (Exception e) {
            Log.e("RECIBO_PDF_ERROR", "Error guardando PDFs", e);
            if (callback != null) {
                callback.onError("Error guardando PDFs: " + e.getMessage());
            }
        }
    }

}
