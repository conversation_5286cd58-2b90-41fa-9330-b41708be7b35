<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="@color/surface_variant">
    <!-- Vista para el nombre de la columna "Código de Producto" -->
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:id="@+id/tvProductCode"
        style="@style/Text.Subtitle"
        android:text="Producto"
        android:textSize="18sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"/>

    <!-- Vista para el nombre de la columna "Tamaño" -->
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:id="@+id/tvSize"
        style="@style/Text.Subtitle"
        android:text="Talla"
        android:gravity="center"
        android:textSize="18sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"/>

    <!-- Vista para el nombre de la columna "Cantidad Seleccionada" -->
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:id="@+id/tvCantidadSeleccionada"
        style="@style/Text.Subtitle"
        android:text="Cantidad"
        android:gravity="center"
        android:textSize="18sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"/>

    <!-- Vista para el nombre de la columna "Precio" -->
    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:id="@+id/tvPrecio"
        style="@style/Text.Subtitle"
        android:text="Precio"
        android:gravity="center"
        android:textSize="18sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"/>

    <TextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:id="@+id/tvAccion"
        style="@style/Text.Subtitle"
        android:text="Acción"
        android:gravity="center"
        android:textSize="18sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"/>


</LinearLayout>
