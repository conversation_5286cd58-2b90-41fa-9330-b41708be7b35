<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="8dp"
    android:background="@color/surface">

    <!-- Vista para el código de producto -->
    <TextView
        android:id="@+id/tvProductCode"
        style="@style/Text.Body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/text_primary"
        android:textSize="16sp"/>

    <!-- Vista para la Tamaño -->
    <TextView
        android:id="@+id/tvSize"
        style="@style/Text.Body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/text_primary"
        android:textSize="16sp"/>

    <!-- Vista para la cantidad seleccionada -->
    <TextView
        android:id="@+id/tvCantidadSeleccionada"
        style="@style/Text.Body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/text_primary"
        android:textSize="16sp"/>

    <!-- Vista para precio -->
    <TextView
        android:id="@+id/tvPrecio"
        style="@style/Text.Body"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:textColor="@color/primary_blue_700"
        android:textSize="16sp"/>

    <Button
        android:id="@+id/btnEliminar"
        style="@style/Button.Error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Borrar" />

</LinearLayout>
