package com.salesmobile;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.content.Context;
import android.content.SharedPreferences;
import android.widget.Button;
import android.widget.Toast;
import com.google.android.material.textfield.TextInputEditText;
import com.salesmobile.ui.login.LoginActivity;

import java.util.Objects;


public class ConexionFragment extends Fragment {
    private TextInputEditText etPuntoVenta, etServidor, etBaseDatos;
    private SharedPreferences sharedPreferences;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_conexion, container, false);

        // Inicialización segura con verificación de null
        etPuntoVenta = view.findViewById(R.id.etPuntoVenta);
        etServidor = view.findViewById(R.id.etServidor);
        etBaseDatos = view.findViewById(R.id.etBaseDatos);

        Button btnGuardar = view.findViewById(R.id.btnGuardarConfig);

        if (etPuntoVenta == null ||  etServidor == null || etBaseDatos == null || btnGuardar == null) {
            throw new IllegalStateException("Error: No se encontraron las vistas en el layout");
        }

        sharedPreferences = requireActivity().getSharedPreferences("ConfigDB", Context.MODE_PRIVATE);
        cargarConfiguracion();

        btnGuardar.setOnClickListener(v -> guardarConfiguracion());

        return view;
    }

    private void cargarConfiguracion() {
        // Verificación adicional de null
        if (getContext() == null || etPuntoVenta == null || etServidor == null || etBaseDatos == null) return;
        etPuntoVenta.setText(sharedPreferences.getString("puntoventa", ""));
        etServidor.setText(sharedPreferences.getString("servidor", ""));
        etBaseDatos.setText(sharedPreferences.getString("basedatos", ""));
    }

    private void guardarConfiguracion() {
        if (getContext() == null || etServidor == null || etBaseDatos == null) return;

        SharedPreferences.Editor editor = sharedPreferences.edit();
        editor.putString("puntoventa", Objects.requireNonNull(etPuntoVenta.getText()).toString().trim());
        editor.putString("servidor", Objects.requireNonNull(etServidor.getText()).toString().trim());
        editor.putString("basedatos", Objects.requireNonNull(etBaseDatos.getText()).toString().trim());
        editor.apply();

        Toast.makeText(getContext(), "Configuración guardada", Toast.LENGTH_SHORT).show();

        if (getActivity() instanceof LoginActivity) {
            ((LoginActivity) requireActivity()).toggleConfiguracion();
        }
    }

}