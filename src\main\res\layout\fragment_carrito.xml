<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fragment_carrito"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    android:padding="16dp"
    tools:context=".ui.inputproduct.CarritoFragment">

    <!-- Encabezado -->
    <TextView
        android:id="@+id/titleCart"
        style="@style/Text.Title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Carrito de Compras"
        android:textColor="@color/text_primary"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textAlignment="center"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Lista de productos -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/gridViewProducts"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="16dp"
        android:clipToPadding="false"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        app:layout_constraintBottom_toTopOf="@+id/totalAmount"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleCart" />

    <!-- Total -->
    <TextView
        android:id="@+id/totalText"
        style="@style/Text.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:text="Total:"
        android:textColor="@color/text_primary"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBaseline_toBaselineOf="@+id/totalAmount"
        app:layout_constraintEnd_toStartOf="@id/totalAmount" />

    <TextView
        android:id="@+id/totalAmount"
        style="@style/Text.Subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        android:text="$0.00"
        android:textColor="@color/primary_blue_700"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@+id/btnCobrar"
        app:layout_constraintEnd_toEndOf="parent" />
        
    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/bottomButtons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="btnCloseCart,btnCobrar" />

    <!-- Botones inferiores -->
    <Button
        android:id="@+id/btnCloseCart"
        style="@style/Button.Secondary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:text="Volver"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnCobrar"
        app:layout_constraintHorizontal_chainStyle="spread"/>

    <Button
        android:id="@+id/btnCobrar"
        style="@style/Button.Primary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:text="Cobrar"
        android:textAllCaps="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/btnCloseCart"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>