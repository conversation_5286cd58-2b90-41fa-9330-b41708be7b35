<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- ========== TEMA ANTERIOR MODO NOCHE (COMENTADO) ========== -->
    <!--
    <style name="Theme.SalesMobile.Old" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:textColor">@color/white</item>
    </style>
    -->

    <!-- ========== NUEVO TEMA PROFESIONAL MODO NOCHE ========== -->
    <!-- Tema principal optimizado para modo oscuro -->
    <style name="Theme.SalesMobile" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Colores principales - Azul más brillante para contraste -->
        <item name="colorPrimary">@color/primary_blue_600</item>
        <item name="colorPrimaryVariant">@color/primary_blue_500</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>

        <!-- Colores secundarios - Verde más visible -->
        <item name="colorSecondary">@color/secondary_green_500</item>
        <item name="colorSecondaryVariant">@color/secondary_green_400</item>
        <item name="colorOnSecondary">@color/text_on_secondary</item>

        <!-- Colores de superficie y fondo oscuros -->
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <!-- Colores de error más suaves -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>

        <!-- Barra de estado oscura -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Colores de texto optimizados para modo oscuro -->
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>

        <!-- Colores de controles para modo oscuro -->
        <item name="colorControlNormal">@color/neutral_gray_500</item>
        <item name="colorControlActivated">@color/primary_blue_600</item>
        <item name="colorControlHighlight">@color/primary_blue_200</item>

        <!-- Divisores oscuros -->
        <item name="android:listDivider">@color/divider</item>

        <!-- Navegación oscura -->
        <item name="android:navigationBarColor">@color/background_primary</item>
        <item name="android:windowLightNavigationBar">false</item>
    </style>

    <!-- ========== VARIACIONES DEL TEMA MODO NOCHE ========== -->
    <style name="Theme.SalesMobile.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.SalesMobile.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.SalesMobile.PopupOverlay" parent="ThemeOverlay.AppCompat.Dark" />

    <!-- ========== ESTILOS DE BOTONES MODO NOCHE ========== -->
    <!-- Botón principal con mayor contraste -->
    <style name="Button.Primary" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary_blue_600</item>
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="rippleColor">@color/primary_blue_200</item>
    </style>

    <!-- Botón secundario con borde brillante -->
    <style name="Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/primary_blue_600</item>
        <item name="android:textColor">@color/primary_blue_600</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
        <item name="rippleColor">@color/primary_blue_200</item>
    </style>

    <!-- ========== ESTILOS ESPECÍFICOS MODO NOCHE ========== -->
    <style name="SpinnerSelectedItem">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="ScannerDialog" parent="Theme.MaterialComponents.DayNight.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowMinWidthMajor">0%</item>
        <item name="android:windowMinWidthMinor">0%</item>
    </style>

    <!-- ========== ESTILOS DE CARDS MODO NOCHE ========== -->
    <style name="Card.Elevated" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">8dp</item>
        <item name="cardBackgroundColor">@color/surface_variant</item>
        <item name="contentPadding">16dp</item>
    </style>

    <style name="Card.Outlined" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">0dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="strokeColor">@color/outline</item>
        <item name="strokeWidth">1dp</item>
        <item name="contentPadding">16dp</item>
    </style>

    <!-- ========== ESTILOS DE TEXTO MODO NOCHE ========== -->
    <style name="Text.Title" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Text.Subtitle" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="Text.Body" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="Text.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textColor">@color/text_secondary</item>
    </style>
</resources>