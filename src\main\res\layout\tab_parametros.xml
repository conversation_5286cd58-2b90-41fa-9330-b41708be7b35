<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            style="?attr/textAppearanceHeadline5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Parámetros del Sistema"
            android:textColor="?android:attr/textColorPrimary" />

        <!-- General Parameters -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="PARÁMETROS GENERALES" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Test:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvTest"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Test PC:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvTestPc"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Entorno:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvEntorno"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- Bancard Parameters -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="PARÁMETROS BANCARD" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Bancard Commerce Code:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvBancardCommerceCode"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Bancard Commerce Branch:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvBancardCommerceBranch"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <!-- URL Parameters -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    style="?attr/textAppearanceHeadline6"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:text="PARÁMETROS DE URL" />

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Dev Bancard URL:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvDevBancardUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Prod Bancard URL:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvProdBancardUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Dev Default URL:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvDevDefaultUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="Prod Default URL:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvProdDefaultUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="App Movil Default URL:">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/tvAppmovilDefaultUrl"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:drawablePadding="8dp"
                        android:enabled="false"
                        android:focusable="false"
                        android:clickable="false"
                        />
                </com.google.android.material.textfield.TextInputLayout>
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>
    </LinearLayout>
</ScrollView>
