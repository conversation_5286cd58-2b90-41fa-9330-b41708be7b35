<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".ui.pay.payFragment">

    <!-- Men<PERSON><PERSON> de éxito con mejor estilo -->

    <!-- Contenedor para los botones con alineación consistente -->
    <TextView
        android:id="@+id/textTitulo"
        style="@style/Text.Title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="24dp"
        android:gravity="center"
        android:lineSpacingExtra="8sp"
        android:text="Gracias por su compra!"
        android:textColor="@color/primary_blue_900"
        android:textSize="28sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/textNroFactura"
        style="@style/Text.Subtitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="24dp"
        android:layout_marginTop="100dp"
        android:layout_marginEnd="18dp"
        android:gravity="center"
        android:text="Nro de Factura"
        android:textColor="@color/primary_blue_900"
        android:textSize="20sp"
        android:textStyle="bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="280dp"
        android:orientation="vertical"
        android:gravity="center_horizontal"
        android:padding="16dp">

        <!-- Botón para imprimir factura -->
        <Button
            android:id="@+id/btnImprimirFactura"
            style="@style/Button.Primary"
            android:layout_width="240dp"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:text="Imprimir Factura"
            android:textAllCaps="false"
            android:textSize="16sp" />

        <!-- Botón para imprimir recibo -->
        <Button
            android:id="@+id/btnImprimirRecibo"
            style="@style/Button.Warning"
            android:layout_width="240dp"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:text="Imprimir Recibo"
            android:textAllCaps="false"
            android:textSize="16sp" />

        <!-- Botón primario con estilo -->
        <Button
            android:id="@+id/otraVenta"
            style="@style/Button.Success"
            android:layout_width="240dp"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:text="Cargar otra Venta"
            android:textAllCaps="false"
            android:textSize="16sp" />

        <!-- Botón secundario con estilo -->
        <Button
            android:id="@+id/home"
            style="@style/Button.Secondary"
            android:layout_width="240dp"
            android:layout_height="48dp"
            android:text="Ir al Inicio"
            android:textAllCaps="false"
            android:textSize="16sp" />
    </LinearLayout>

    <!-- Progress Bar para operaciones de impresión -->
    <ProgressBar
        android:id="@+id/progressBarImprimir"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="200dp"
        android:indeterminateTint="@color/primary_blue_600"
        android:visibility="gone" />

</FrameLayout>