package com.salesmobile.ui.client;

import com.salesmobile.config.ParametrosConf;
import android.content.Context;
import android.os.Bundle;

import androidx.fragment.app.Fragment;
import androidx.navigation.NavController;
import androidx.navigation.Navigation;

import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.util.Patterns;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.Toast;

import com.android.volley.AuthFailureError;
import com.android.volley.DefaultRetryPolicy;
import com.android.volley.Request;
import com.android.volley.RequestQueue;
import com.android.volley.TimeoutError;
import com.android.volley.toolbox.JsonObjectRequest;
import com.android.volley.toolbox.StringRequest;
import com.android.volley.toolbox.Volley;
import com.salesmobile.AppDatabase;
import com.salesmobile.Configuracion;
import com.salesmobile.R;
import com.salesmobile.TokenRequest;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;



import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.function.Consumer;

import okhttp3.ResponseBody;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;
import okhttp3.ResponseBody;



public class ClientFragment extends Fragment implements TokenRequest.TokenRequestListener {
    private EditText editNroDocumento;
    public EditText editNombreClient;
    public EditText editEmail;
    private String token;
    private String baseUrl;
    private String CodEmpresa;
    private String CodSucursal;
    private String fechaFormateada;
    private ProgressBar progressBarCliente;
    public String codigoCliente;
    public String BASE_URL = ParametrosConf.BASE_URL; // URL base de la API
    public String API2_URL = ParametrosConf.API2_URL; // URL del datacenter
    public String API1_URL = ParametrosConf.API1_URL; // URL del host

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        obtenerToken(); // Obtener el token al iniciar el fragmento
    }

    private void obtenerBaseUrl(Consumer<String> callback) {
        Executors.newSingleThreadExecutor().execute(() -> {
            AppDatabase db = AppDatabase.getInstance(requireContext());
            Configuracion configuracion = db.configuracionDao().obtenerConfiguracion();
            String url = configuracion != null ? configuracion.getBaseUrl() : ParametrosConf.APPMOVIL_BASE_URL;
            String empresa = configuracion != null ? configuracion.getCodigoEmpresa() : "1";
            String sucursal = configuracion != null ? configuracion.getCodigoSucursal() : "1";

            // Actualizar la UI en el hilo principal
            requireActivity().runOnUiThread(() -> {
                baseUrl = API1_URL;
                CodEmpresa = empresa;
                CodSucursal = sucursal;
                callback.accept(url);
            });
        });
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_client, container, false);
        Bundle args = getArguments();
        boolean abiertoDesdeMenu = args != null && args.getBoolean("abiertoDesdeMenu", false);
        boolean nuevo = args != null && args.getBoolean("nuevo", false);
        int fragmentoOrigen = args != null ? args.getInt("fragmentoOrigen", R.id.nav_home) : R.id.nav_home;
        progressBarCliente = view.findViewById(R.id.progressBarCliente);
        editNroDocumento = view.findViewById(R.id.editNroDocumento);
        editNombreClient = view.findViewById(R.id.editNombreClient);
        editEmail = view.findViewById(R.id.editEmail);
        Button btnCancelar = view.findViewById(R.id.btnCancelar);
        Button btnGuardar = view.findViewById(R.id.btnGuardar);

        obtenerBaseUrl(url -> {

            btnGuardar.setOnClickListener(v -> {

                String nombreCliente = editNombreClient.getText().toString();
                String numeroDocumento = editNroDocumento.getText().toString();
                String email = editEmail.getText().toString();

                if (!Patterns.EMAIL_ADDRESS.matcher(email).matches()) {
                    editEmail.setError("Correo no válido");
                    editEmail.requestFocus();
                    return;
                }
                guardarCliente(nombreCliente, numeroDocumento, email, token, abiertoDesdeMenu);
            });

            btnCancelar.setOnClickListener(v -> {
                NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                navController.navigate(fragmentoOrigen); // Navega al fragmento de origen
            });

            editNroDocumento.setOnFocusChangeListener((v, hasFocus) -> {
                String numeroDoc = editNroDocumento.getText().toString().trim();
                String nombreCliente = editNombreClient.getText().toString();

                // Caso 1: Cuando PIERDE el foco (consultar siempre)
                if (!hasFocus && !numeroDoc.isEmpty() && nombreCliente.isEmpty()) {
                    if (!nuevo) {
                        consultarCliente(numeroDoc);
                    }
                }
                // Caso 2: Cuando RECIBE el foco (solo si viene de Home sin datos)
                /*
                    else if (hasFocus && numeroDoc == null && !numeroDoc.isEmpty()) {
                        if (!nuevo) {
                            consultarCliente(numeroDoc);
                        }
                    }
                */
            });

            String nroDocumento2 = getArguments() != null ? getArguments().getString("nroDocumento") : null;
            String nroDocumento = (nroDocumento2 != null) ? nroDocumento2.trim() : null;

            if (nroDocumento != null) {
                editNroDocumento.setText(nroDocumento);
            }

            // Forzar foco y teclado (opcional)
            editNroDocumento.post(() -> {
                editNroDocumento.requestFocus();
                InputMethodManager imm = (InputMethodManager) requireActivity().getSystemService(Context.INPUT_METHOD_SERVICE);
               if (imm != null) {
                    imm.showSoftInput(editNroDocumento, InputMethodManager.SHOW_IMPLICIT);
                }


            // Llamar a consultarCliente() si hay texto (por ejemplo, si nroDocumento != null)
            if (nroDocumento != null && !nroDocumento.isEmpty()) {
                if (!nuevo) {
                    consultarCliente(nroDocumento);
                }
            }
            });
        });
        return view;
    }

    private void obtenerToken() {
        // Pasar el contexto al constructor de TokenRequest
        TokenRequest tokenRequest = new TokenRequest(requireContext(), new TokenRequest.TokenRequestListener() {
            @Override
            public void onTokenReceived(String receivedToken) {
                token = receivedToken;
            }
        });
        tokenRequest.execute();
    }

    private void consultarCliente(String nroDocumento) {
        if (baseUrl == null) {
            Toast.makeText(getContext(), "URL base no configurada", Toast.LENGTH_SHORT).show();
            return;
        }

        progressBarCliente.setVisibility(View.VISIBLE);

        String url = API1_URL + "/api/CLIENTE?NumeroDocumento1=" + nroDocumento;
        RequestQueue queue = Volley.newRequestQueue(requireContext());
        StringRequest stringRequest = new StringRequest(Request.Method.GET, url,
                response -> {
                    try {
                        JSONObject jsonResponse = new JSONObject(response);
                        progressBarCliente.setVisibility(View.GONE);

                        // Verificar si CLIENTES es null
                        if (jsonResponse.isNull("CLIENTES")) {
                            Log.d("DEBUG", "Cliente no encontrado en la base de datos.");
                            Toast.makeText(getContext(), "Cliente no encontrado", Toast.LENGTH_SHORT).show();
                            editNombreClient.setText("");
                            editEmail.setText("");
                            return;
                        }

                        JSONObject clientesObj = jsonResponse.getJSONObject("CLIENTES");
                        Log.d("GUARDAR_CLIENTE", "Contenido de CLIENTES: " + clientesObj.toString());
                        Object clienteData = clientesObj.get("Cliente"); // Puede ser JSONArray o JSONObject

                        if (clienteData instanceof JSONArray) {
                            JSONArray clientesArray = (JSONArray) clienteData;
                            if (clientesArray.length() > 0) {
                                JSONObject cliente = clientesArray.getJSONObject(0);
                                String nombre = cliente.getString("Nombre");
                                String email = cliente.optString("Email", "");
                                codigoCliente = cliente.getString("Codigo");
                                editNombreClient.setText(nombre);
                                editEmail.setText(email);
                            }
                        } else if (clienteData instanceof JSONObject) {
                            JSONObject cliente = (JSONObject) clienteData;
                            String nombre = cliente.getString("Nombre");
                            String email = cliente.optString("Email", "");
                            codigoCliente = cliente.getString("Codigo");
                            editNombreClient.setText(nombre);
                            editEmail.setText(email);
                        }

                    } catch (JSONException e) {
                        Log.e("JSONError", "Error procesando JSON", e);
                        progressBarCliente.setVisibility(View.GONE);
                        Toast.makeText(getContext(), "Error al procesar la respuesta", Toast.LENGTH_LONG).show();
                    }
                },
                error -> {
                    progressBarCliente.setVisibility(View.GONE);
                    Log.e("ERROR", "Error en la petición de la API", error);
                    Toast.makeText(getContext(), "Error al buscar el cliente", Toast.LENGTH_SHORT).show();

                }) {
            @Override
            public Map<String, String> getHeaders() throws AuthFailureError {
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + token);
                headers.put("accept", "*/*");
                return headers;
            }
        };

        // Configurar el tiempo de espera a 10 segundos
        stringRequest.setRetryPolicy(new DefaultRetryPolicy(
                10000, // Tiempo de espera en milisegundos (10 segundos)
                DefaultRetryPolicy.DEFAULT_MAX_RETRIES, // Número de reintentos
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT) // Multiplicador de reintento
        );

        queue.add(stringRequest);
        Log.d("DEBUG", "Petición agregada a la cola de Volley.");
    }

    private void guardarCliente(String nombre, String documento, String email, String token, boolean abiertoDesdeMenu) {
        if (baseUrl == null || token == null) {
            Log.e("GUARDAR_CLIENTE", "Token o baseUrl no están listos. baseUrl=" + baseUrl + " token=" + token);
            Toast.makeText(getContext(), "Token o URL no disponible", Toast.LENGTH_SHORT).show();
            return;
        }

        progressBarCliente.setVisibility(View.VISIBLE);
        String[] partes = documento.split("-");
        String codigoFiltrado = partes.length > 0 ? partes[0] : documento;
        if (codigoFiltrado.length() > 8) {
            codigoFiltrado = codigoFiltrado.substring(0, 8);
        }

        JSONObject clienteRequest = new JSONObject();

        try {
            JSONObject header = new JSONObject();
            header.put("etiqueta", "Cliente");
            header.put("codemp", CodEmpresa);
            header.put("codsuc", CodSucursal);
            header.put("Fecha", fechaFormateada);

            JSONObject cliente = new JSONObject();
            cliente.put("codigo", codigoFiltrado);
            cliente.put("nombre", nombre);
            cliente.put("nombreFantasia", nombre);
            cliente.put("localidad", "CAPITAL");
            cliente.put("codigoDocumento", documento.contains("-") ? "11" : "12");
            cliente.put("codigoPais", "2");
            cliente.put("email", email);
            cliente.put("numeroDocumento1", documento);

            // LÓGICA CORREGIDA:
            if (codigoCliente == null || codigoCliente.isEmpty()) {
                cliente.put("operacion", "A"); // Alta (nuevo cliente)
                cliente.put("codigo", "T" + codigoFiltrado); // Si el cliente es nuevo ponemos una T en el predijo del codigo
            } else {
                cliente.put("operacion", "M"); // Modificación (cliente existente)
                cliente.put("codigo", codigoCliente); // Usamos el código real del cliente
            }

            // Configuración de condiciones de venta
            JSONArray condicionesVenta = new JSONArray();
            JSONObject condicion = new JSONObject();
            condicion.put("Codigo", "V01");
            condicion.put("Estado", "S");
            condicion.put("listaEstandar", "001");
            condicion.put("listaOferta", "001");
            condicion.put("listaMinima", "001");
            if (codigoCliente == null || codigoCliente.isEmpty()) {
                condicion.put("operacion", "A");
            } else {
                condicion.put("operacion", "M");
            }
            condicionesVenta.put(condicion);
            cliente.put("CondicionesVenta", condicionesVenta);

            // Configuración de atributos
            JSONArray atributosValores = new JSONArray();
            JSONObject atributos = new JSONObject();
            atributos.put("codigo", "C03");
            atributos.put("valor", "2");
            if (codigoCliente == null || codigoCliente.isEmpty()) {
                atributos.put("operacion", "A");
            } else {
                atributos.put("operacion", "M");
            }
            atributosValores.put(atributos);
            cliente.put("atributosValores", atributosValores);

            clienteRequest.put("Header", header);
            clienteRequest.put("Cliente", cliente);

            Log.d("API_REQUEST", "Cuerpo de la solicitud: " + clienteRequest.toString());

            // LÓGICA DE ENVÍO CORREGIDA:
            if (codigoCliente == null || codigoCliente.isEmpty()) {
                // Cliente nuevo - POST
                realizarSolicitud(Request.Method.POST, API1_URL + "/api/CLIENTE", clienteRequest,
                        "Cliente creado exitosamente", abiertoDesdeMenu);
            } else {
                // Cliente existente - PUT
                realizarSolicitud(Request.Method.PUT, API1_URL + "/api/CLIENTE", clienteRequest,
                        "Cliente actualizado exitosamente", abiertoDesdeMenu);
            }

        } catch (JSONException e) {
            progressBarCliente.setVisibility(View.GONE);
            Log.e("JSONError", "Error creando el JSON de la solicitud", e);
            //Toast.makeText(getContext(), "Error al crear el JSON de la solicitud", Toast.LENGTH_LONG).show();
        }
    }

    private void realizarSolicitud(int method, String url, JSONObject clienteRequest, String mensajeExito, boolean abiertoDesdeMenu) {
        Log.d("DEBUG_REALIZAR", "Entró a realizarSolicitud con método: " + method);

        JsonObjectRequest request = new JsonObjectRequest(method, url, clienteRequest,
                response -> {
                    progressBarCliente.setVisibility(View.GONE);
                    Log.d("API_RESPONSE", "Respuesta de la API: " + response.toString()); // Log de la respuesta
                    Toast.makeText(getContext(), mensajeExito, Toast.LENGTH_LONG).show();

                    if (!abiertoDesdeMenu) {
                        Log.d("GUARDAR_CLIENTE", "Entro a la condicion abiertoDesdeMenu");
                        // Crear un Bundle para enviar datos al selectClientFragment
                        Bundle args = new Bundle();
                        args.putString("nombreCliente", Objects.requireNonNull(clienteRequest.optJSONObject("Cliente")).optString("nombre"));
                        args.putString("nroDocumento", Objects.requireNonNull(clienteRequest.optJSONObject("Cliente")).optString("numeroDocumento1"));
                        args.putString("email", Objects.requireNonNull(clienteRequest.optJSONObject("Cliente")).optString("email"));
                        args.putString("codigoCliente", Objects.requireNonNull(clienteRequest.optJSONObject("Cliente")).optString("codigo"));
                        args.putBoolean("clienteGuardado", true); // Indicador de que el cliente se guardó correctamente

                        // Navegar al selectClientFragment con los datos
                        NavController navController = Navigation.findNavController(requireActivity(), R.id.nav_host_fragment_content_main);
                        navController.navigate(R.id.nav_selectClientFragment, args);
                    }
                },
                error -> {
                    progressBarCliente.setVisibility(View.GONE);
                    // Manejo de errores
                    if (error instanceof TimeoutError) {
                        // Error de timeout
                        Toast.makeText(getContext(), "La solicitud tardó demasiado. Por favor, intenta de nuevo.", Toast.LENGTH_LONG).show();
                        Log.e("API_ERROR", "TimeoutError: La solicitud tardó demasiado.");
                    } else if (error.networkResponse != null) {
                        // Error de red o del servidor
                        String errorMessage = "Error en la solicitud: ";
                        switch (error.networkResponse.statusCode) {
                            case 400:
                                errorMessage += "Solicitud incorrecta.";
                                break;
                            case 401:
                                errorMessage += "No autorizado.";
                                break;
                            case 500:
                                errorMessage += "Error interno del servidor.";
                                break;
                            default:
                                errorMessage += "Código de estado: " + error.networkResponse.statusCode;
                                break;
                        }
                        Toast.makeText(getContext(), errorMessage, Toast.LENGTH_LONG).show();
                        Log.e("API_ERROR", errorMessage);
                    } else {
                        // Otros errores
                        Toast.makeText(getContext(), "Error desconocido. Por favor, intenta de nuevo.", Toast.LENGTH_LONG).show();
                        Log.e("API_ERROR", "Error desconocido: " + error.toString());
                    }
                }) {
            @Override
            public Map<String, String> getHeaders() {
                Map<String, String> headers = new HashMap<>();
                headers.put("Authorization", "Bearer " + token);
                headers.put("Content-Type", "application/json");
                return headers;
            }
        };

        // Configurar el tiempo de espera a 10 segundos
        request.setRetryPolicy(new DefaultRetryPolicy(
                10000, // Tiempo de espera en milisegundos (10 segundos)
                DefaultRetryPolicy.DEFAULT_MAX_RETRIES, // Número de reintentos
                DefaultRetryPolicy.DEFAULT_BACKOFF_MULT) // Multiplicador de reintento
        );

        RequestQueue queue = Volley.newRequestQueue(requireContext());
        queue.add(request);
    }


    @Override
    public void onTokenReceived(String token) {
        this.token = token;
    }
}