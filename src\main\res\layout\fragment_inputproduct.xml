<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:padding="16dp"
    tools:context=".ui.inputproduct.InputProductFragment">

    <!-- Título -->
    <TextView
        android:id="@+id/text_inputProduct"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Catálogo de Productos"
        android:textAlignment="center"
        android:textSize="22sp"
        android:textStyle="bold"
        android:textColor="@color/primary_blue_600"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Card de Búsqueda de Producto -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/searchCard"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        app:cardElevation="4dp"
        app:cardCornerRadius="8dp"
        app:layout_constraintTop_toBottomOf="@id/text_inputProduct"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textFieldProductCode"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:hint="Ingrese el código de producto"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextProductCode"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="text"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonAceptar"
                style="@style/Widget.MaterialComponents.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="Buscar"
                android:backgroundTint="@color/primary_blue_600"
                app:layout_constraintTop_toBottomOf="@id/textFieldProductCode"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/buttonScanBarcode"
                style="@style/Widget.MaterialComponents.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="Escanear"
                app:icon="@android:drawable/ic_menu_camera"
                app:layout_constraintTop_toBottomOf="@id/buttonAceptar"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/btnShowCart"
                android:layout_marginEnd="8dp"
                app:layout_constraintHorizontal_chainStyle="packed"
                />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/btnShowCart"
                style="@style/Widget.MaterialComponents.Button.TextButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:text="Carrito"
                app:icon="@drawable/ic_shopping_cart"
                app:layout_constraintTop_toTopOf="@id/buttonScanBarcode"
                app:layout_constraintStart_toEndOf="@id/buttonScanBarcode"
                app:layout_constraintEnd_toEndOf="parent"
                 />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.google.android.material.card.MaterialCardView>


    <!-- Card de Detalles del Producto -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/productDetailsCard"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="16dp"
        app:cardElevation="4dp"
        app:cardCornerRadius="8dp"
        app:layout_constraintTop_toBottomOf="@id/searchCard"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:visibility="gone"
        tools:visibility="visible">

        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="16dp">

                <TextView
                    android:id="@+id/textDescripcion"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:text="Descripción del Producto" />

                <TextView
                    android:id="@+id/textPrecio"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:textColor="@color/primary_blue_600"
                    android:textSize="16sp"
                    app:layout_constraintTop_toBottomOf="@id/textDescripcion"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    tools:text="$0.00" />

                <TextView
                    android:id="@+id/textView2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Cantidad:"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/textPrecio"
                    app:layout_constraintBottom_toBottomOf="@id/numberSpinner"/>

                <Spinner
                    android:id="@+id/numberSpinner"
                    android:layout_width="80dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="16dp"
                    android:background="@drawable/spinner_background"
                    android:spinnerMode="dropdown"
                    app:layout_constraintStart_toEndOf="@id/textView2"
                    app:layout_constraintTop_toTopOf="@id/textView2" />

                <TextView
                    android:id="@+id/textViewTalla"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:text="Talla:"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/numberSpinner" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerViewSizes"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:clipToPadding="false"
                    app:layout_constraintTop_toBottomOf="@id/textViewTalla"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/buttonAddProduct"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="24dp"
                    android:backgroundTint="@color/alt4_primary_navy_600"
                    android:paddingVertical="12dp"
                    android:text="Agregar"
                    android:textSize="16sp"
                    app:layout_constraintTop_toBottomOf="@id/recyclerViewSizes"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </ScrollView>
    </com.google.android.material.card.MaterialCardView>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBarInput"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/purple_500"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>