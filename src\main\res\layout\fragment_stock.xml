<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="8dp"
    tools:context=".ui.stock.StockFragment">

    <!-- Contened<PERSON> de b<PERSON>queda -->
    <LinearLayout
        android:id="@+id/searchContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <EditText
            android:id="@+id/editTextProductCode"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:hint="Código de producto"
            android:inputType="text"/>



        <Button
            android:id="@+id/buttonCheckStock"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Buscar"
            android:layout_marginStart="8dp"/>
    </LinearLayout>

    <!-- Encabezados de columnas -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@color/primary_blue_500"
        android:padding="8dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.2"
            android:text="Sucursal"
            android:textColor="@android:color/white"
            android:textStyle="bold"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.5"
            android:text="Nombre Sucursal"
            android:textColor="@android:color/white"
            android:textStyle="bold"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.15"
            android:text="Talla"
            android:textColor="@android:color/white"
            android:textStyle="bold"/>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.15"
            android:text="Stock"
            android:textColor="@android:color/white"
            android:textStyle="bold"/>
    </LinearLayout>

    <ProgressBar
        android:id="@+id/progressBarStock"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:visibility="gone"/>
    <!-- RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerViewStock"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="8dp"
        android:background="@android:color/white"/>
</LinearLayout>