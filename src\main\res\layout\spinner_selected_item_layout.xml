<!-- spinner_selected_item_layout.xml -->
<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@android:id/text1"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:textSize="16sp"
    app:tabTextColor="@color/text_on_primary"
    app:tabSelectedTextColor="@color/text_on_primary"
    app:tabIndicatorColor="@color/text_on_primary"
    app:tabRippleColor="@color/primary_blue_200"
    android:padding="8dp"
    android:gravity="center" />
