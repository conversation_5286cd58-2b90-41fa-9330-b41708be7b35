<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="24dp"
    android:background="@color/background_primary"
    tools:context="ui.login.LoginActivity">

    <ImageView
        android:id="@+id/logo"
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginTop="32dp"
        android:src="@drawable/logosalesmovile"
        android:scaleType="centerCrop"
        android:contentDescription="@string/salesmobile"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <TextView
        android:id="@+id/title"
        style="@style/Text.Title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/aplicaci_n_de_ventas"
        android:textSize="20sp"
        android:textColor="@color/text_primary"
        android:textStyle="bold"
        android:layout_marginTop="24dp"
        app:layout_constraintTop_toBottomOf="@id/logo"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/tilUsuario"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="32dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etUsuarioSQL"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="@string/c_digo_de_vendedor"
            android:inputType="text"
            android:textColor="@color/text_primary"
            android:maxLines="1"/>
    </com.google.android.material.textfield.TextInputLayout>

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/tilPassword"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox"
        app:layout_constraintTop_toBottomOf="@id/tilUsuario"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:endIconMode="password_toggle">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/etPasswordSQL"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:hint="Contraseña Vendedor"
            android:inputType="textPassword"
            android:textColor="@color/text_primary"
            android:maxLines="1"/>
    </com.google.android.material.textfield.TextInputLayout>

    <CheckBox
        android:id="@+id/cbRecordar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Recordar contraseña"
        android:textColor="@color/text_secondary"
        android:buttonTint="@color/primary_blue_600"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/tilPassword"
        app:layout_constraintStart_toStartOf="parent"
        tools:ignore="HardcodedText" />

    <Button
        android:id="@+id/btnLogin"
        style="@style/Button.Primary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/conectar"
        android:layout_marginTop="16dp"
        app:layout_constraintTop_toBottomOf="@id/cbRecordar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>
        
    <androidx.constraintlayout.widget.Group
        android:id="@+id/login_form"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:constraint_referenced_ids="logo,title,tilUsuario,tilPassword,cbRecordar,btnLogin"
        />

    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:indeterminateTint="@color/primary_blue_600"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/btnLogin"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="16dp"/>

    <Button
        android:id="@+id/btnConfiguracion"
        style="@style/Button.Secondary"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="Configurar Conexión"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        tools:ignore="HardcodedText,VisualLintButtonSize" />

    <FrameLayout
        android:id="@+id/fragment_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
