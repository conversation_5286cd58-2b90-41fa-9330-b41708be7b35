package com.salesmobile.utils;

import android.content.Context;
import android.util.Base64;
import android.util.Log;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class generadorFacturas {

    public interface FacturaCallback {
        void onSuccess(String pdfFilePath);
        void onError(String errorMessage);
    }

    public static void generarFacturaDesdeRespuesta(Context context, String response, File cachedPdfFile, FacturaCallback callback) {
        if (cachedPdfFile != null && cachedPdfFile.exists()) {
            Log.d("FACTURA_CACHE", "Usando PDF desde cache: " + cachedPdfFile.getAbsolutePath());
            if (callback != null) {
                callback.onSuccess(cachedPdfFile.getAbsolutePath());
            }
            return;
        }

        new Thread(() -> {
            try {
                JSONObject jsonResponse = new JSONObject(response);

                if (jsonResponse.has("success") && jsonResponse.getBoolean("success")) {
                    JSONObject data = jsonResponse.getJSONObject("data");
                    if (data.has("pdfBase64")) {
                        String pdfBase64 = data.getString("pdfBase64");
                        Log.d("FACTURA_GEN", "pdfBase64 extraído, longitud: " + pdfBase64.length());

                        if (pdfBase64.startsWith("data:application/pdf")) {
                            pdfBase64 = pdfBase64.substring(pdfBase64.indexOf(",") + 1);
                        }

                        byte[] pdfBytes = Base64.decode(pdfBase64, Base64.DEFAULT);
                        Log.d("FACTURA_GEN", "PDF decodificado, tamaño: " + pdfBytes.length + " bytes");

                        File pdfFile = new File(context.getCacheDir(), "factura_" + System.currentTimeMillis() + ".pdf");
                        FileOutputStream fos = new FileOutputStream(pdfFile);
                        fos.write(pdfBytes);
                        fos.close();

                        Log.d("FACTURA_GEN", "Archivo PDF guardado: " + pdfFile.getAbsolutePath());

                        if (callback != null) {
                            callback.onSuccess(pdfFile.getAbsolutePath());
                        }

                    } else {
                        Log.e("FACTURA_GEN", "No se encontró pdfBase64 en la respuesta");
                        if (callback != null) {
                            callback.onError("No se encontró PDF en la respuesta");
                        }
                    }
                } else {
                    Log.e("FACTURA_GEN", "Respuesta no exitosa: " + jsonResponse);
                    if (callback != null) {
                        callback.onError("Respuesta no exitosa del servidor");
                    }
                }

            } catch (JSONException | IOException e) {
                Log.e("FACTURA_GEN", "Error al procesar respuesta: " + e.getMessage());
                if (callback != null) {
                    callback.onError("Error al procesar respuesta: " + e.getMessage());
                }
            }
        }).start();
    }

    public static void generarFactura(Context context, JSONObject facturaData, File cachedPdfFile, FacturaCallback callback) {
        if (cachedPdfFile != null && cachedPdfFile.exists()) {
            Log.d("FACTURA_CACHE", "Usando PDF desde cache: " + cachedPdfFile.getAbsolutePath());
            if (callback != null) {
                callback.onSuccess(cachedPdfFile.getAbsolutePath());
            }
            return;
        }

        Log.d("FACTURA_GEN", "Generando nuevo PDF via enviarEmail API");

        enviarEmail.enviarEmail(context, facturaData, false, true, new enviarEmail.EmailCallback() {
            @Override
            public void onSuccess(String response) {
                new Thread(() -> {
                    try {
                        JSONObject jsonResponse = new JSONObject(response);

                        if (jsonResponse.has("success") && jsonResponse.getBoolean("success")) {
                            JSONObject data = jsonResponse.getJSONObject("data");
                            if (data.has("pdfBase64")) {
                                String pdfBase64 = data.getString("pdfBase64");
                                Log.d("FACTURA_GEN", "pdfBase64 extraído, longitud: " + pdfBase64.length());

                                if (pdfBase64.startsWith("data:application/pdf")) {
                                    pdfBase64 = pdfBase64.substring(pdfBase64.indexOf(",") + 1);
                                }

                                byte[] pdfBytes = Base64.decode(pdfBase64, Base64.DEFAULT);
                                Log.d("FACTURA_GEN", "PDF decodificado, tamaño: " + pdfBytes.length + " bytes");

                                File pdfFile = new File(context.getCacheDir(), "factura_" + System.currentTimeMillis() + ".pdf");
                                FileOutputStream fos = new FileOutputStream(pdfFile);
                                fos.write(pdfBytes);
                                fos.close();

                                Log.d("FACTURA_GEN", "Archivo PDF guardado: " + pdfFile.getAbsolutePath());

                                if (callback != null) {
                                    callback.onSuccess(pdfFile.getAbsolutePath());
                                }

                            } else {
                                Log.e("FACTURA_GEN", "No se encontró pdfBase64 en la respuesta");
                                if (callback != null) {
                                    callback.onError("No se encontró PDF en la respuesta");
                                }
                            }
                        } else {
                            Log.e("FACTURA_GEN", "Respuesta no exitosa: " + jsonResponse);
                            if (callback != null) {
                                callback.onError("Respuesta no exitosa del servidor");
                            }
                        }

                    } catch (JSONException | IOException e) {
                        Log.e("FACTURA_GEN", "Error al procesar respuesta: " + e.getMessage());
                        if (callback != null) {
                            callback.onError("Error al procesar respuesta: " + e.getMessage());
                        }
                    }
                }).start();
            }

            @Override
            public void onError(String errorMessage) {
                Log.e("FACTURA_GEN", "Error en enviarEmail API: " + errorMessage);
                if (callback != null) {
                    callback.onError("Error en enviarEmail API: " + errorMessage);
                }
            }
        });
    }
}
