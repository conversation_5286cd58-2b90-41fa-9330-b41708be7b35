<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_secondary"
    android:fillViewport="true">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="20dp"
    tools:context=".ui.client.selectClientFragment">

    <!-- Search Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/surface">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Búsqueda de Cliente"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/textView3"
                    style="@style/Text.Body"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.35"
                    android:text="Nro. Documento:"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/editNroDocumento"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.65"
                    android:ems="10"
                    android:inputType="text"
                    android:hint="Buscar Cliente"
                    android:background="@drawable/edittext_background"
                    android:padding="14dp"
                    android:textColor="@color/text_primary"
                    android:textColorHint="@color/text_hint"
                    android:elevation="2dp"/>
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Client Information Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/surface">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Información del Cliente"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <!-- Nombre del Cliente -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/info_light"
                android:padding="12dp"
                android:layout_marginBottom="12dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_clients"
                    android:layout_marginEnd="8dp"
                    app:tint="@color/info_dark" />

                <TextView
                    android:id="@+id/textNombre"
                    style="@style/Text.Body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Nombre del Cliente"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:visibility="visible" />
            </LinearLayout>

            <!-- Correo del Cliente -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:background="@color/info_light"
                android:padding="12dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_business"
                    android:layout_marginEnd="8dp"
                    app:tint="@color/info_dark" />

                <TextView
                    android:id="@+id/textCorreo"
                    style="@style/Text.Body"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Correo del Cliente"
                    android:textColor="@color/text_primary"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:visibility="visible" />
            </LinearLayout>

        </LinearLayout>
    </androidx.cardview.widget.CardView>


    <!-- Action Buttons Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp"
        app:cardBackgroundColor="@color/surface">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="20dp">

            <!-- Botón Generar QR -->
            <Button
                android:id="@+id/btnQr"
                style="@style/Button.Warning"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:text="Generar QR"
                android:textAllCaps="false"
                android:textSize="16sp"
                android:visibility="gone"/>

            <!-- Botón Modificar Cliente -->
            <Button
                android:id="@+id/btnModificar"
                style="@style/Button.Secondary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Modificar Cliente"
                android:textAllCaps="false"
                android:textSize="16sp"
                android:visibility="gone"/>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Progress Bar -->
    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="20dp"
        app:cardCornerRadius="50dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/surface">

        <ProgressBar
            android:id="@+id/progressBarClienteBuscar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:indeterminateTint="@color/primary_blue_600"
            android:visibility="gone"/>

    </androidx.cardview.widget.CardView>

    <!-- Navigation Buttons Card -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="6dp"
        app:cardBackgroundColor="@color/surface">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center">

            <Button
                android:id="@+id/btnVolver"
                style="@style/Button.Secondary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="Volver"
                android:textAllCaps="false"
                android:textSize="16sp"/>

            <Button
                android:id="@+id/btnContinue"
                style="@style/Button.Primary"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="Buscar"
                android:textAllCaps="false"
                android:textSize="16sp"/>

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</LinearLayout>

</ScrollView>