<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- ========== TEMA ANTERIOR (COMENTADO) ========== -->
    <!--
    <style name="Theme.SalesMobile.Old" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:textColor">@color/black</item>
    </style>
    -->

    <!-- ========== NUEVO TEMA PROFESIONAL ========== -->
    <!-- Te<PERSON> principal de la aplicación -->
    <style name="Theme.SalesMobile" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        <!-- Colores principales -->
        <item name="colorPrimary">@color/primary_blue_600</item>
        <item name="colorPrimaryVariant">@color/primary_blue_800</item>
        <item name="colorOnPrimary">@color/text_on_primary</item>

        <!-- Colores secundarios -->
        <item name="colorSecondary">@color/secondary_green_500</item>
        <item name="colorSecondaryVariant">@color/secondary_green_700</item>
        <item name="colorOnSecondary">@color/text_on_secondary</item>

        <!-- Colores de superficie y fondo -->
        <item name="android:colorBackground">@color/background_primary</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnSurface">@color/text_primary</item>
        <item name="colorOnBackground">@color/text_primary</item>

        <!-- Colores de error -->
        <item name="colorError">@color/error</item>
        <item name="colorOnError">@color/white</item>

        <!-- Barra de estado -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Colores de texto por defecto -->
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textColorSecondary">@color/text_secondary</item>
        <item name="android:textColorHint">@color/text_hint</item>

        <!-- Colores de controles -->
        <item name="colorControlNormal">@color/neutral_gray_600</item>
        <item name="colorControlActivated">@color/primary_blue_600</item>
        <item name="colorControlHighlight">@color/primary_blue_100</item>

        <!-- Divisores -->
        <item name="android:listDivider">@color/divider</item>

        <!-- Estilo por defecto para CardViews -->
        <item name="materialCardViewStyle">@style/Card.Base</item>
    </style>

    <!-- ========== VARIACIONES DEL TEMA ========== -->
    <style name="Theme.SalesMobile.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.SalesMobile.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="Theme.SalesMobile.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <!-- ========== ESTILOS DE BOTONES ========== -->
    <!-- Botón principal -->
    <style name="Button.Primary" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary_blue_600</item>
        <item name="android:textColor">@color/text_on_primary</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>

    <!-- Botón secundario -->
    <style name="Button.Secondary" parent="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="strokeColor">@color/primary_blue_600</item>
        <item name="android:textColor">@color/primary_blue_600</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
        <item name="android:paddingStart">24dp</item>
        <item name="android:paddingEnd">24dp</item>
    </style>

    <!-- Botón de éxito -->
    <style name="Button.Success" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/success</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- Botón de error/cancelar -->
    <style name="Button.Error" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/error</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- Botón de advertencia -->
    <style name="Button.Warning" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/warning</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- ========== ESTILOS DE TEXTO ========== -->
    <!-- Título principal -->
    <style name="Text.Title" parent="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- Subtítulo -->
    <style name="Text.Subtitle" parent="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <!-- Texto del cuerpo -->
    <style name="Text.Body" parent="TextAppearance.MaterialComponents.Body1">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <!-- Texto secundario -->
    <style name="Text.Caption" parent="TextAppearance.MaterialComponents.Caption">
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <!-- ========== ESTILOS ESPECÍFICOS ========== -->
    <style name="SpinnerSelectedItem">
        <item name="android:textSize">20sp</item>
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="ScannerDialog" parent="Theme.MaterialComponents.DayNight.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowMinWidthMajor">0%</item>
        <item name="android:windowMinWidthMinor">0%</item>
    </style>

    <!-- ========== ESTILOS DE CARDS Y CONTENEDORES ========== -->
    <!-- Estilo base para todas las CardViews -->
    <style name="Card.Base" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">6dp</item>
        <item name="cardBackgroundColor">@color/surface</item>
        <item name="contentPadding">16dp</item>
        <item name="cardUseCompatPadding">true</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- Card elevada con sombras mejoradas -->
    <style name="Card.Elevated" parent="Card.Base">
        <item name="cardElevation">8dp</item>
        <item name="cardMaxElevation">12dp</item>
    </style>

    <!-- Card con borde y sombra sutil -->
    <style name="Card.Outlined" parent="Card.Base">
        <item name="cardElevation">4dp</item>
        <item name="strokeColor">@color/outline</item>
        <item name="strokeWidth">1dp</item>
    </style>

    <!-- Card con sombra alta para elementos importantes -->
    <style name="Card.HighElevation" parent="Card.Base">
        <item name="cardElevation">12dp</item>
        <item name="cardMaxElevation">16dp</item>
        <item name="cardCornerRadius">16dp</item>
    </style>

    <!-- Card plana sin sombra -->
    <style name="Card.Flat" parent="Card.Base">
        <item name="cardElevation">0dp</item>
        <item name="cardUseCompatPadding">false</item>
    </style>


</resources>