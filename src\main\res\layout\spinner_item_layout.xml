<TextView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@android:id/text1"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:tabTextColor="@color/text_on_primary"
    app:tabSelectedTextColor="@color/text_on_primary"
    app:tabIndicatorColor="@color/text_on_primary"
    app:tabRippleColor="@color/primary_blue_200"
    android:textSize="20sp"
    android:gravity="center_vertical"
    android:paddingStart="8dp"
    android:paddingEnd="8dp"
    android:paddingTop="4dp"
    android:paddingBottom="4dp" />
